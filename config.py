"""
Configuration module for database automation
"""
import os

# Try to load environment variables from .env file if python-dotenv is available
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # If python-dotenv is not available, just use os.environ
    print("python-dotenv not available, using os.environ directly")
    print("Make sure to set environment variables manually or edit this file with your credentials")

class DatabaseConfig:
    """Database connection configurations"""
    
    # Oracle Configuration
    ORACLE_USER = os.getenv('ORACLE_USER')
    ORACLE_PASSWORD = os.getenv('ORACLE_PASSWORD')
    ORACLE_HOST = os.getenv('ORACLE_HOST')
    ORACLE_PORT = os.getenv('ORACLE_PORT', '1521')
    ORACLE_SERVICE_NAME = os.getenv('ORACLE_SERVICE_NAME')
    
    @property
    def oracle_dsn(self):
        return f"{self.ORACLE_HOST}:{self.ORACLE_PORT}/{self.ORACLE_SERVICE_NAME}"
    
    # MySQL Configuration
    MYSQL_HOST = os.getenv('MYSQL_HOST')
    MYSQL_PORT = int(os.getenv('MYSQL_PORT', '3306'))
    MYSQL_USER = os.getenv('MYSQL_USER')
    MYSQL_PASSWORD = os.getenv('MYSQL_PASSWORD')
    MYSQL_DATABASE = os.getenv('MYSQL_DATABASE')

class EmailConfig:
    """Email configuration for sending reports"""
    
    SMTP_SERVER = os.getenv('SMTP_SERVER', 'smtp.gmail.com')
    SMTP_PORT = int(os.getenv('SMTP_PORT', '587'))
    EMAIL_USER = os.getenv('EMAIL_USER')
    EMAIL_PASSWORD = os.getenv('EMAIL_PASSWORD')
    CLIENT_EMAIL = os.getenv('CLIENT_EMAIL')

class ReportConfig:
    """Report generation configuration"""
    
    REPORT_TITLE = os.getenv('REPORT_TITLE', 'Database Merge Report')
    OUTPUT_DIR = os.getenv('OUTPUT_DIR', 'reports')
    SCHEDULE_TIME = os.getenv('SCHEDULE_TIME', '09:00')
    
    @classmethod
    def ensure_output_dir(cls):
        """Ensure output directory exists"""
        if not os.path.exists(cls.OUTPUT_DIR):
            os.makedirs(cls.OUTPUT_DIR)
