"""
Email functionality for sending reports
"""
import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultipart
from email.mime.base import MIMEBase
from email import encoders
from datetime import datetime
from config import EmailConfig

logger = logging.getLogger(__name__)

class EmailSender:
    """Handles email sending functionality"""
    
    def __init__(self):
        self.config = EmailConfig()
    
    def send_html_email(self, 
                       html_content: str,
                       subject: str = None,
                       recipient: str = None,
                       attachment_path: str = None) -> bool:
        """
        Send HTML email with optional attachment
        
        Args:
            html_content: HTML content to send
            subject: Email subject
            recipient: Recipient email address
            attachment_path: Path to file attachment (optional)
        
        Returns:
            True if sent successfully, False otherwise
        """
        try:
            if subject is None:
                subject = f"Database Report - {datetime.now().strftime('%Y-%m-%d')}"
            
            if recipient is None:
                recipient = self.config.CLIENT_EMAIL
            
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = self.config.EMAIL_USER
            msg['To'] = recipient
            
            # Add HTML content
            html_part = MIMEText(html_content, 'html')
            msg.attach(html_part)
            
            # Add attachment if provided
            if attachment_path:
                self._add_attachment(msg, attachment_path)
            
            # Send email
            with smtplib.SMTP(self.config.SMTP_SERVER, self.config.SMTP_PORT) as server:
                server.starttls()
                server.login(self.config.EMAIL_USER, self.config.EMAIL_PASSWORD)
                server.send_message(msg)
            
            logger.info(f"Email sent successfully to {recipient}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending email: {e}")
            return False
    
    def _add_attachment(self, msg: MIMEMultipart, file_path: str):
        """Add file attachment to email message"""
        try:
            with open(file_path, "rb") as attachment:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(attachment.read())
            
            encoders.encode_base64(part)
            
            filename = file_path.split('/')[-1]  # Get filename from path
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {filename}'
            )
            
            msg.attach(part)
            logger.info(f"Added attachment: {filename}")
            
        except Exception as e:
            logger.error(f"Error adding attachment: {e}")
            raise
    
    def send_simple_email(self, 
                         message: str,
                         subject: str = None,
                         recipient: str = None) -> bool:
        """
        Send simple text email
        
        Args:
            message: Text message to send
            subject: Email subject
            recipient: Recipient email address
        
        Returns:
            True if sent successfully, False otherwise
        """
        try:
            if subject is None:
                subject = f"Database Automation Notification - {datetime.now().strftime('%Y-%m-%d')}"
            
            if recipient is None:
                recipient = self.config.CLIENT_EMAIL
            
            msg = MIMEText(message)
            msg['Subject'] = subject
            msg['From'] = self.config.EMAIL_USER
            msg['To'] = recipient
            
            with smtplib.SMTP(self.config.SMTP_SERVER, self.config.SMTP_PORT) as server:
                server.starttls()
                server.login(self.config.EMAIL_USER, self.config.EMAIL_PASSWORD)
                server.send_message(msg)
            
            logger.info(f"Simple email sent successfully to {recipient}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending simple email: {e}")
            return False
    
    def test_email_connection(self) -> bool:
        """Test email server connection"""
        try:
            with smtplib.SMTP(self.config.SMTP_SERVER, self.config.SMTP_PORT) as server:
                server.starttls()
                server.login(self.config.EMAIL_USER, self.config.EMAIL_PASSWORD)
            
            logger.info("Email connection test: SUCCESS")
            return True
            
        except Exception as e:
            logger.error(f"Email connection test: FAILED - {e}")
            return False
