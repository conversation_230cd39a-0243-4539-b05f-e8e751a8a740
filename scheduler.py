"""
Scheduler for automating database reports
"""
import schedule
import time
import logging
from datetime import datetime
from db_automation import DatabaseAutomation
from config import ReportConfig

logger = logging.getLogger(__name__)

class AutomationScheduler:
    """Handles scheduling of database automation tasks"""
    
    def __init__(self):
        self.automation = DatabaseAutomation()
        self.config = ReportConfig()
    
    def daily_report_job(self):
        """Job function for daily reports"""
        logger.info("Starting scheduled daily report...")
        
        # Define your queries here
        oracle_query = """
        SELECT 
            employee_id as id,
            first_name,
            last_name,
            department_id,
            salary,
            hire_date
        FROM employees 
        WHERE hire_date >= SYSDATE - 1
        """
        
        mysql_query = """
        SELECT 
            emp_id as id,
            email,
            phone,
            status,
            last_login,
            created_date
        FROM employee_details 
        WHERE DATE(created_date) = CURDATE() - INTERVAL 1 DAY
        """
        
        merge_config = {
            'type': 'merge',
            'on': 'id',
            'how': 'inner'
        }
        
        success = self.automation.run_automation(
            oracle_query=oracle_query,
            mysql_query=mysql_query,
            merge_config=merge_config,
            send_email=True,
            save_file=True
        )
        
        if success:
            logger.info("Scheduled daily report completed successfully")
        else:
            logger.error("Scheduled daily report failed")
    
    def weekly_report_job(self):
        """Job function for weekly reports"""
        logger.info("Starting scheduled weekly report...")
        
        # Define your weekly queries here
        oracle_query = """
        SELECT 
            employee_id as id,
            first_name,
            last_name,
            department_id,
            salary,
            hire_date
        FROM employees 
        WHERE hire_date >= SYSDATE - 7
        """
        
        mysql_query = """
        SELECT 
            emp_id as id,
            email,
            phone,
            status,
            last_login,
            created_date
        FROM employee_details 
        WHERE created_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        """
        
        merge_config = {
            'type': 'concatenate',
            'add_source_column': True
        }
        
        success = self.automation.run_automation(
            oracle_query=oracle_query,
            mysql_query=mysql_query,
            merge_config=merge_config,
            send_email=True,
            save_file=True
        )
        
        if success:
            logger.info("Scheduled weekly report completed successfully")
        else:
            logger.error("Scheduled weekly report failed")
    
    def setup_schedule(self):
        """Setup the schedule for automated reports"""
        
        # Daily report at configured time
        schedule.every().day.at(self.config.SCHEDULE_TIME).do(self.daily_report_job)
        
        # Weekly report every Monday at 8:00 AM
        schedule.every().monday.at("08:00").do(self.weekly_report_job)
        
        # You can add more schedules as needed:
        # schedule.every().hour.do(self.hourly_job)
        # schedule.every().friday.at("17:00").do(self.weekly_summary)
        
        logger.info(f"Scheduler configured:")
        logger.info(f"- Daily reports at {self.config.SCHEDULE_TIME}")
        logger.info(f"- Weekly reports every Monday at 08:00")
    
    def run_scheduler(self):
        """Run the scheduler continuously"""
        self.setup_schedule()
        
        logger.info("Scheduler started. Press Ctrl+C to stop.")
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
        except KeyboardInterrupt:
            logger.info("Scheduler stopped by user")
        except Exception as e:
            logger.error(f"Scheduler error: {e}")

def main():
    """Main function to start the scheduler"""
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('scheduler.log'),
            logging.StreamHandler()
        ]
    )
    
    scheduler = AutomationScheduler()
    
    # Test connections before starting scheduler
    logger.info("Testing connections before starting scheduler...")
    connection_results = scheduler.automation.test_connections()
    
    if not all(connection_results.values()):
        logger.error("Some connections failed. Please check configuration.")
        logger.error(f"Connection results: {connection_results}")
        return
    
    logger.info("All connections successful. Starting scheduler...")
    scheduler.run_scheduler()

if __name__ == "__main__":
    main()
