"""
Simple database connection and query management
"""
import logging
from contextlib import contextmanager
from typing import List, Dict, Any
from config import DatabaseConfig

logger = logging.getLogger(__name__)

class SimpleDatabaseManager:
    """Manages database connections and queries with fallback options"""
    
    def __init__(self):
        self.config = DatabaseConfig()
        self.oracle_available = False
        self.mysql_available = False
        
        # Check which database drivers are available
        try:
            import cx_Oracle
            self.oracle_available = True
            logger.info("Oracle driver (cx_Oracle) is available")
        except ImportError:
            logger.warning("Oracle driver (cx_Oracle) not available")
        
        try:
            import mysql.connector
            self.mysql_available = True
            logger.info("MySQL driver (mysql-connector-python) is available")
        except ImportError:
            logger.warning("MySQL driver (mysql-connector-python) not available")
    
    @contextmanager
    def oracle_connection(self):
        """Context manager for Oracle database connection"""
        if not self.oracle_available:
            raise ImportError("cx_Oracle not available. Please install: pip install cx_Oracle")
        
        import cx_Oracle
        conn = None
        try:
            conn = cx_Oracle.connect(
                user=self.config.ORACLE_USER,
                password=self.config.ORACLE_PASSWORD,
                dsn=self.config.oracle_dsn
            )
            logger.info("Oracle connection established")
            yield conn
        except Exception as e:
            logger.error(f"Oracle connection error: {e}")
            raise
        finally:
            if conn:
                conn.close()
                logger.info("Oracle connection closed")
    
    @contextmanager
    def mysql_connection(self):
        """Context manager for MySQL database connection"""
        if not self.mysql_available:
            raise ImportError("mysql-connector-python not available. Please install: pip install mysql-connector-python")
        
        import mysql.connector
        conn = None
        try:
            conn = mysql.connector.connect(
                host=self.config.MYSQL_HOST,
                port=self.config.MYSQL_PORT,
                user=self.config.MYSQL_USER,
                password=self.config.MYSQL_PASSWORD,
                database=self.config.MYSQL_DATABASE
            )
            logger.info("MySQL connection established")
            yield conn
        except Exception as e:
            logger.error(f"MySQL connection error: {e}")
            raise
        finally:
            if conn:
                conn.close()
                logger.info("MySQL connection closed")
    
    def fetch_oracle_data(self, query: str) -> List[Dict]:
        """Fetch data from Oracle database and return as list of dictionaries"""
        if not self.oracle_available:
            logger.error("Oracle driver not available")
            return self._get_sample_oracle_data()
        
        try:
            with self.oracle_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query)
                
                # Get column names
                columns = [desc[0] for desc in cursor.description]
                
                # Fetch all rows
                rows = cursor.fetchall()
                
                # Convert to list of dictionaries
                data = []
                for row in rows:
                    row_dict = dict(zip(columns, row))
                    data.append(row_dict)
                
                logger.info(f"Fetched {len(data)} rows from Oracle")
                return data
        except Exception as e:
            logger.error(f"Error fetching Oracle data: {e}")
            logger.info("Returning sample data for testing")
            return self._get_sample_oracle_data()
    
    def fetch_mysql_data(self, query: str) -> List[Dict]:
        """Fetch data from MySQL database and return as list of dictionaries"""
        if not self.mysql_available:
            logger.error("MySQL driver not available")
            return self._get_sample_mysql_data()
        
        try:
            with self.mysql_connection() as conn:
                cursor = conn.cursor(dictionary=True)
                cursor.execute(query)
                
                # Fetch all rows as dictionaries
                data = cursor.fetchall()
                
                logger.info(f"Fetched {len(data)} rows from MySQL")
                return data
        except Exception as e:
            logger.error(f"Error fetching MySQL data: {e}")
            logger.info("Returning sample data for testing")
            return self._get_sample_mysql_data()
    
    def _get_sample_oracle_data(self) -> List[Dict]:
        """Return sample Oracle data for testing when database is not available"""
        return [
            {'ID': 1, 'FIRST_NAME': 'John', 'LAST_NAME': 'Doe', 'DEPARTMENT_ID': 10, 'SALARY': 50000},
            {'ID': 2, 'FIRST_NAME': 'Jane', 'LAST_NAME': 'Smith', 'DEPARTMENT_ID': 20, 'SALARY': 60000},
            {'ID': 3, 'FIRST_NAME': 'Bob', 'LAST_NAME': 'Johnson', 'DEPARTMENT_ID': 10, 'SALARY': 55000},
            {'ID': 4, 'FIRST_NAME': 'Alice', 'LAST_NAME': 'Brown', 'DEPARTMENT_ID': 30, 'SALARY': 65000},
        ]
    
    def _get_sample_mysql_data(self) -> List[Dict]:
        """Return sample MySQL data for testing when database is not available"""
        return [
            {'id': 1, 'email': '<EMAIL>', 'phone': '+1234567890', 'status': 'active'},
            {'id': 2, 'email': '<EMAIL>', 'phone': '+1234567891', 'status': 'active'},
            {'id': 3, 'email': '<EMAIL>', 'phone': '+1234567892', 'status': 'inactive'},
            {'id': 4, 'email': '<EMAIL>', 'phone': '+1234567893', 'status': 'active'},
        ]
    
    def test_connections(self) -> Dict[str, bool]:
        """Test both database connections"""
        results = {}
        
        # Test Oracle
        if self.oracle_available:
            try:
                with self.oracle_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1 FROM DUAL")
                    cursor.fetchone()
                    results['oracle'] = True
                    logger.info("Oracle connection test: SUCCESS")
            except Exception as e:
                results['oracle'] = False
                logger.error(f"Oracle connection test: FAILED - {e}")
        else:
            results['oracle'] = False
            logger.warning("Oracle driver not available")
        
        # Test MySQL
        if self.mysql_available:
            try:
                with self.mysql_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("SELECT 1")
                    cursor.fetchone()
                    results['mysql'] = True
                    logger.info("MySQL connection test: SUCCESS")
            except Exception as e:
                results['mysql'] = False
                logger.error(f"MySQL connection test: FAILED - {e}")
        else:
            results['mysql'] = False
            logger.warning("MySQL driver not available")
        
        return results
