"""
Data processing and merging logic
"""
import pandas as pd
import logging
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

class DataProcessor:
    """Handles data merging and processing operations"""
    
    def __init__(self):
        pass
    
    def merge_dataframes(self, 
                        oracle_df: pd.DataFrame, 
                        mysql_df: pd.DataFrame,
                        merge_on: str = 'id',
                        merge_type: str = 'inner') -> pd.DataFrame:
        """
        Merge Oracle and MySQL dataframes
        
        Args:
            oracle_df: DataFrame from Oracle
            mysql_df: DataFrame from MySQL
            merge_on: Column name to merge on
            merge_type: Type of merge ('inner', 'outer', 'left', 'right')
        
        Returns:
            Merged DataFrame
        """
        try:
            if merge_on not in oracle_df.columns:
                raise ValueError(f"Column '{merge_on}' not found in Oracle data")
            if merge_on not in mysql_df.columns:
                raise ValueError(f"Column '{merge_on}' not found in MySQL data")
            
            merged_df = pd.merge(
                oracle_df, 
                mysql_df, 
                on=merge_on, 
                how=merge_type,
                suffixes=('_oracle', '_mysql')
            )
            
            logger.info(f"Merged data: {len(merged_df)} rows from {len(oracle_df)} Oracle + {len(mysql_df)} MySQL rows")
            return merged_df
            
        except Exception as e:
            logger.error(f"Error merging dataframes: {e}")
            raise
    
    def concatenate_dataframes(self, 
                              oracle_df: pd.DataFrame, 
                              mysql_df: pd.DataFrame,
                              add_source_column: bool = True) -> pd.DataFrame:
        """
        Concatenate Oracle and MySQL dataframes
        
        Args:
            oracle_df: DataFrame from Oracle
            mysql_df: DataFrame from MySQL
            add_source_column: Whether to add a source identifier column
        
        Returns:
            Concatenated DataFrame
        """
        try:
            if add_source_column:
                oracle_df = oracle_df.copy()
                mysql_df = mysql_df.copy()
                oracle_df['data_source'] = 'Oracle'
                mysql_df['data_source'] = 'MySQL'
            
            concatenated_df = pd.concat([oracle_df, mysql_df], ignore_index=True)
            
            logger.info(f"Concatenated data: {len(concatenated_df)} total rows")
            return concatenated_df
            
        except Exception as e:
            logger.error(f"Error concatenating dataframes: {e}")
            raise
    
    def apply_filters(self, df: pd.DataFrame, filters: Dict) -> pd.DataFrame:
        """
        Apply filters to the dataframe
        
        Args:
            df: Input DataFrame
            filters: Dictionary of column:value filters
        
        Returns:
            Filtered DataFrame
        """
        try:
            filtered_df = df.copy()
            
            for column, value in filters.items():
                if column in filtered_df.columns:
                    if isinstance(value, list):
                        filtered_df = filtered_df[filtered_df[column].isin(value)]
                    else:
                        filtered_df = filtered_df[filtered_df[column] == value]
                    logger.info(f"Applied filter {column}={value}, remaining rows: {len(filtered_df)}")
                else:
                    logger.warning(f"Filter column '{column}' not found in data")
            
            return filtered_df
            
        except Exception as e:
            logger.error(f"Error applying filters: {e}")
            raise
    
    def select_columns(self, df: pd.DataFrame, columns: List[str]) -> pd.DataFrame:
        """
        Select specific columns from dataframe
        
        Args:
            df: Input DataFrame
            columns: List of column names to select
        
        Returns:
            DataFrame with selected columns
        """
        try:
            available_columns = [col for col in columns if col in df.columns]
            missing_columns = [col for col in columns if col not in df.columns]
            
            if missing_columns:
                logger.warning(f"Missing columns: {missing_columns}")
            
            if not available_columns:
                raise ValueError("No valid columns found")
            
            selected_df = df[available_columns].copy()
            logger.info(f"Selected {len(available_columns)} columns")
            
            return selected_df
            
        except Exception as e:
            logger.error(f"Error selecting columns: {e}")
            raise
