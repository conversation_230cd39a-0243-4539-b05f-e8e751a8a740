"""
Simplified database automation script that works with minimal dependencies
"""
import logging
import sys
import os
from datetime import datetime
from typing import Dict, Optional

from simple_database_manager import SimpleDatabaseManager
from simple_data_processor import SimpleDataProcessor
from html_generator import HTMLGenerator
from email_sender import <PERSON>ail<PERSON><PERSON>
from config import ReportConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('simple_automation.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class SimpleAutomation:
    """Simplified automation class that works with minimal dependencies"""
    
    def __init__(self):
        self.db_manager = SimpleDatabaseManager()
        self.data_processor = SimpleDataProcessor()
        self.html_generator = HTMLGenerator()
        self.email_sender = EmailSender()
        self.config = ReportConfig()
    
    def run_automation(self, 
                      oracle_query: str = None,
                      mysql_query: str = None,
                      merge_config: Dict = None,
                      filters: Dict = None,
                      columns: list = None,
                      send_email: bool = True,
                      save_file: bool = True) -> bool:
        """
        Run the complete automation process
        
        Args:
            oracle_query: SQL query for Oracle database
            mysql_query: SQL query for MySQL database
            merge_config: Configuration for merging data
            filters: Filters to apply to merged data
            columns: Specific columns to include in output
            send_email: Whether to send email
            save_file: Whether to save HTML file
        
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info("Starting simplified database automation process")
            
            # Use default queries if none provided
            if oracle_query is None:
                oracle_query = "SELECT 1 as id, 'Sample' as name FROM DUAL"
            if mysql_query is None:
                mysql_query = "SELECT 1 as id, '<EMAIL>' as email"
            
            # Step 1: Fetch data from both databases
            logger.info("Fetching data from databases...")
            oracle_data = self.db_manager.fetch_oracle_data(oracle_query)
            mysql_data = self.db_manager.fetch_mysql_data(mysql_query)
            
            if not oracle_data and not mysql_data:
                logger.warning("No data retrieved from either database")
                return False
            
            # Step 2: Process and merge data
            logger.info("Processing and merging data...")
            merged_data = self._process_data(oracle_data, mysql_data, merge_config)
            
            # Step 3: Apply filters if specified
            if filters:
                logger.info("Applying filters...")
                merged_data = self.data_processor.apply_filters(merged_data, filters)
            
            # Step 4: Select specific columns if specified
            if columns:
                logger.info("Selecting specific columns...")
                merged_data = self.data_processor.select_columns(merged_data, columns)
            
            if not merged_data:
                logger.warning("No data remaining after processing")
                return False
            
            # Step 5: Convert to table format for HTML generation
            table_data = self.data_processor.dict_list_to_table(merged_data)
            
            # Step 6: Generate HTML report
            logger.info("Generating HTML report...")
            html_content = self._generate_html_from_table(table_data)
            
            # Step 7: Save file if requested
            file_path = None
            if save_file:
                logger.info("Saving HTML file...")
                file_path = self.html_generator.save_html_file(html_content)
            
            # Step 8: Send email if requested
            if send_email:
                logger.info("Sending email...")
                success = self.email_sender.send_html_email(
                    html_content=html_content,
                    attachment_path=file_path
                )
                if not success:
                    logger.error("Failed to send email")
                    return False
            
            logger.info("Simplified automation completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Automation failed: {e}")
            # Send error notification email
            self._send_error_notification(str(e))
            return False
    
    def _process_data(self, oracle_data, mysql_data, merge_config):
        """Process and merge the data based on configuration"""
        if merge_config is None:
            merge_config = {'type': 'concatenate'}
        
        merge_type = merge_config.get('type', 'concatenate')
        
        if merge_type == 'merge':
            merge_on = merge_config.get('on', 'id')
            how = merge_config.get('how', 'inner')
            return self.data_processor.merge_data(
                oracle_data, mysql_data, merge_on=merge_on, merge_type=how
            )
        elif merge_type == 'concatenate':
            add_source = merge_config.get('add_source_column', True)
            return self.data_processor.concatenate_data(
                oracle_data, mysql_data, add_source_column=add_source
            )
        else:
            raise ValueError(f"Unknown merge type: {merge_type}")
    
    def _generate_html_from_table(self, table_data):
        """Generate HTML from table data"""
        # Create a simple HTML table
        html_table = "<table class='table table-striped table-bordered'>\n"
        
        # Add header
        if table_data['columns']:
            html_table += "<thead><tr>\n"
            for col in table_data['columns']:
                html_table += f"<th>{col}</th>\n"
            html_table += "</tr></thead>\n"
        
        # Add body
        html_table += "<tbody>\n"
        for row in table_data['rows']:
            html_table += "<tr>\n"
            for cell in row:
                html_table += f"<td>{cell}</td>\n"
            html_table += "</tr>\n"
        html_table += "</tbody>\n"
        html_table += "</table>\n"
        
        # Use the HTML generator to create the full styled document
        return self.html_generator._create_styled_html(
            html_table, 
            self.config.REPORT_TITLE,
            type('MockDF', (), {'__len__': lambda: len(table_data['rows']), 'columns': table_data['columns']})()
        )
    
    def _send_error_notification(self, error_message: str):
        """Send error notification email"""
        try:
            subject = f"Database Automation Error - {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            message = f"""
Database automation process failed with the following error:

Error: {error_message}
Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Please check the logs for more details.
            """
            self.email_sender.send_simple_email(message, subject)
        except Exception as e:
            logger.error(f"Failed to send error notification: {e}")
    
    def test_connections(self) -> Dict[str, bool]:
        """Test all connections (databases and email)"""
        logger.info("Testing all connections...")
        
        results = {}
        
        # Test database connections
        db_results = self.db_manager.test_connections()
        results.update(db_results)
        
        # Test email connection
        results['email'] = self.email_sender.test_email_connection()
        
        logger.info(f"Connection test results: {results}")
        return results

def main():
    """Example usage of the simplified automation system"""
    
    print("=" * 60)
    print("🚀 SIMPLIFIED DATABASE AUTOMATION")
    print("=" * 60)
    
    # Example queries - replace with your actual queries
    oracle_query = """
    SELECT 
        1 as id,
        'John' as first_name,
        'Doe' as last_name,
        10 as department_id,
        50000 as salary
    FROM DUAL
    UNION ALL
    SELECT 
        2 as id,
        'Jane' as first_name,
        'Smith' as last_name,
        20 as department_id,
        60000 as salary
    FROM DUAL
    """
    
    mysql_query = """
    SELECT 
        1 as id,
        '<EMAIL>' as email,
        '+1234567890' as phone,
        'active' as status
    UNION ALL
    SELECT 
        2 as id,
        '<EMAIL>' as email,
        '+1234567891' as phone,
        'active' as status
    """
    
    # Merge configuration
    merge_config = {
        'type': 'merge',  # or 'concatenate'
        'on': 'id',
        'how': 'inner'
    }
    
    # Optional filters
    filters = {
        'status': 'active'
    }
    
    # Optional column selection
    columns = ['id', 'first_name', 'last_name', 'email', 'salary', 'status']
    
    # Run automation
    automation = SimpleAutomation()
    
    # Test connections first
    print("Testing connections...")
    connection_results = automation.test_connections()
    
    print("\nConnection Results:")
    for service, status in connection_results.items():
        status_text = "✅ SUCCESS" if status else "❌ FAILED"
        print(f"{service.upper():<10}: {status_text}")
    
    print("\nRunning automation with sample data...")
    
    # Run the automation
    success = automation.run_automation(
        oracle_query=oracle_query,
        mysql_query=mysql_query,
        merge_config=merge_config,
        filters=filters,
        columns=columns,
        send_email=False,  # Set to True when email is configured
        save_file=True
    )
    
    if success:
        print("🎉 Automation completed successfully!")
        print("Check the 'reports' folder for the generated HTML report.")
    else:
        print("❌ Automation failed! Check the logs for details.")

if __name__ == "__main__":
    main()
