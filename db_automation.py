"""
Main database automation script
"""
import logging
import sys
from datetime import datetime
from typing import Dict, Optional

from database_manager import DatabaseManager
from data_processor import DataProcessor
from html_generator import HTM<PERSON>Generator
from email_sender import <PERSON>ailSender
from config import ReportConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('db_automation.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class DatabaseAutomation:
    """Main automation class that orchestrates the entire process"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.data_processor = DataProcessor()
        self.html_generator = HTMLGenerator()
        self.email_sender = EmailSender()
        self.config = ReportConfig()
    
    def run_automation(self, 
                      oracle_query: str,
                      mysql_query: str,
                      merge_config: Dict = None,
                      filters: Dict = None,
                      columns: list = None,
                      send_email: bool = True,
                      save_file: bool = True) -> bool:
        """
        Run the complete automation process
        
        Args:
            oracle_query: SQL query for Oracle database
            mysql_query: SQL query for MySQL database
            merge_config: Configuration for merging data
            filters: Filters to apply to merged data
            columns: Specific columns to include in output
            send_email: Whether to send email
            save_file: Whether to save HTML file
        
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info("Starting database automation process")
            
            # Step 1: Fetch data from both databases
            logger.info("Fetching data from databases...")
            oracle_df = self.db_manager.fetch_oracle_data(oracle_query)
            mysql_df = self.db_manager.fetch_mysql_data(mysql_query)
            
            if oracle_df.empty and mysql_df.empty:
                logger.warning("No data retrieved from either database")
                return False
            
            # Step 2: Process and merge data
            logger.info("Processing and merging data...")
            merged_df = self._process_data(oracle_df, mysql_df, merge_config)
            
            # Step 3: Apply filters if specified
            if filters:
                logger.info("Applying filters...")
                merged_df = self.data_processor.apply_filters(merged_df, filters)
            
            # Step 4: Select specific columns if specified
            if columns:
                logger.info("Selecting specific columns...")
                merged_df = self.data_processor.select_columns(merged_df, columns)
            
            if merged_df.empty:
                logger.warning("No data remaining after processing")
                return False
            
            # Step 5: Generate HTML report
            logger.info("Generating HTML report...")
            html_content = self.html_generator.generate_html_table(merged_df)
            
            # Step 6: Save file if requested
            file_path = None
            if save_file:
                logger.info("Saving HTML file...")
                file_path = self.html_generator.save_html_file(html_content)
            
            # Step 7: Send email if requested
            if send_email:
                logger.info("Sending email...")
                success = self.email_sender.send_html_email(
                    html_content=html_content,
                    attachment_path=file_path
                )
                if not success:
                    logger.error("Failed to send email")
                    return False
            
            logger.info("Database automation completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Automation failed: {e}")
            # Send error notification email
            self._send_error_notification(str(e))
            return False
    
    def _process_data(self, oracle_df, mysql_df, merge_config):
        """Process and merge the data based on configuration"""
        if merge_config is None:
            merge_config = {'type': 'concatenate'}
        
        merge_type = merge_config.get('type', 'concatenate')
        
        if merge_type == 'merge':
            merge_on = merge_config.get('on', 'id')
            how = merge_config.get('how', 'inner')
            return self.data_processor.merge_dataframes(
                oracle_df, mysql_df, merge_on=merge_on, merge_type=how
            )
        elif merge_type == 'concatenate':
            add_source = merge_config.get('add_source_column', True)
            return self.data_processor.concatenate_dataframes(
                oracle_df, mysql_df, add_source_column=add_source
            )
        else:
            raise ValueError(f"Unknown merge type: {merge_type}")
    
    def _send_error_notification(self, error_message: str):
        """Send error notification email"""
        try:
            subject = f"Database Automation Error - {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            message = f"""
Database automation process failed with the following error:

Error: {error_message}
Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Please check the logs for more details.
            """
            self.email_sender.send_simple_email(message, subject)
        except Exception as e:
            logger.error(f"Failed to send error notification: {e}")
    
    def test_connections(self) -> Dict[str, bool]:
        """Test all connections (databases and email)"""
        logger.info("Testing all connections...")
        
        results = {}
        
        # Test database connections
        db_results = self.db_manager.test_connections()
        results.update(db_results)
        
        # Test email connection
        results['email'] = self.email_sender.test_email_connection()
        
        logger.info(f"Connection test results: {results}")
        return results

def main():
    """Example usage of the automation system"""
    
    # Example queries - replace with your actual queries
    oracle_query = """
    SELECT 
        employee_id as id,
        first_name,
        last_name,
        department_id,
        salary
    FROM employees 
    WHERE hire_date >= SYSDATE - 30
    """
    
    mysql_query = """
    SELECT 
        emp_id as id,
        email,
        phone,
        status,
        last_login
    FROM employee_details 
    WHERE status = 'active'
    """
    
    # Merge configuration
    merge_config = {
        'type': 'merge',  # or 'concatenate'
        'on': 'id',
        'how': 'inner'
    }
    
    # Optional filters
    filters = {
        'department_id': [10, 20, 30],  # Only specific departments
        'status': 'active'
    }
    
    # Optional column selection
    columns = ['id', 'first_name', 'last_name', 'email', 'salary', 'status']
    
    # Run automation
    automation = DatabaseAutomation()
    
    # Test connections first
    connection_results = automation.test_connections()
    if not all(connection_results.values()):
        logger.error("Some connections failed. Please check configuration.")
        return
    
    # Run the automation
    success = automation.run_automation(
        oracle_query=oracle_query,
        mysql_query=mysql_query,
        merge_config=merge_config,
        filters=filters,
        columns=columns,
        send_email=True,
        save_file=True
    )
    
    if success:
        logger.info("Automation completed successfully!")
    else:
        logger.error("Automation failed!")

if __name__ == "__main__":
    main()
