"""
Database connection and query management
"""
import cx_Oracle
import mysql.connector
import pandas as pd
import logging
from contextlib import contextmanager
from config import DatabaseConfig

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages database connections and queries"""
    
    def __init__(self):
        self.config = DatabaseConfig()
    
    @contextmanager
    def oracle_connection(self):
        """Context manager for Oracle database connection"""
        conn = None
        try:
            conn = cx_Oracle.connect(
                user=self.config.ORACLE_USER,
                password=self.config.ORACLE_PASSWORD,
                dsn=self.config.oracle_dsn
            )
            logger.info("Oracle connection established")
            yield conn
        except Exception as e:
            logger.error(f"Oracle connection error: {e}")
            raise
        finally:
            if conn:
                conn.close()
                logger.info("Oracle connection closed")
    
    @contextmanager
    def mysql_connection(self):
        """Context manager for MySQL database connection"""
        conn = None
        try:
            conn = mysql.connector.connect(
                host=self.config.MYSQL_HOST,
                port=self.config.MYSQL_PORT,
                user=self.config.MYSQL_USER,
                password=self.config.MYSQL_PASSWORD,
                database=self.config.MYSQL_DATABASE
            )
            logger.info("MySQL connection established")
            yield conn
        except Exception as e:
            logger.error(f"MySQL connection error: {e}")
            raise
        finally:
            if conn:
                conn.close()
                logger.info("MySQL connection closed")
    
    def fetch_oracle_data(self, query):
        """Fetch data from Oracle database"""
        try:
            with self.oracle_connection() as conn:
                df = pd.read_sql(query, conn)
                logger.info(f"Fetched {len(df)} rows from Oracle")
                return df
        except Exception as e:
            logger.error(f"Error fetching Oracle data: {e}")
            raise
    
    def fetch_mysql_data(self, query):
        """Fetch data from MySQL database"""
        try:
            with self.mysql_connection() as conn:
                df = pd.read_sql(query, conn)
                logger.info(f"Fetched {len(df)} rows from MySQL")
                return df
        except Exception as e:
            logger.error(f"Error fetching MySQL data: {e}")
            raise
    
    def test_connections(self):
        """Test both database connections"""
        results = {}
        
        # Test Oracle
        try:
            with self.oracle_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1 FROM DUAL")
                cursor.fetchone()
                results['oracle'] = True
                logger.info("Oracle connection test: SUCCESS")
        except Exception as e:
            results['oracle'] = False
            logger.error(f"Oracle connection test: FAILED - {e}")
        
        # Test MySQL
        try:
            with self.mysql_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                results['mysql'] = True
                logger.info("MySQL connection test: SUCCESS")
        except Exception as e:
            results['mysql'] = False
            logger.error(f"MySQL connection test: FAILED - {e}")
        
        return results
