# Database Automation System

A comprehensive Python-based automation system that fetches data from Oracle and MySQL databases, merges the data, and delivers beautifully formatted HTML reports to clients via email.

## 🚀 Features

- **Multi-Database Support**: Connect to both Oracle and MySQL databases
- **Flexible Data Merging**: Merge or concatenate data with various options
- **Beautiful HTML Reports**: Generate styled, responsive HTML tables
- **Email Delivery**: Automatically send reports via email with attachments
- **Scheduling**: Built-in scheduler for automated daily/weekly reports
- **Error Handling**: Comprehensive logging and error notifications
- **Configuration Management**: Environment-based configuration
- **Connection Testing**: Built-in tools to test all connections

## 📁 Project Structure

```
DBAutomation/
├── config.py              # Configuration management
├── database_manager.py    # Database connections and queries
├── data_processor.py      # Data merging and processing
├── html_generator.py      # HTML report generation
├── email_sender.py        # Email functionality
├── db_automation.py       # Main automation orchestrator
├── scheduler.py           # Automated scheduling
├── test_connections.py    # Connection testing utility
├── run_once.py           # Single run utility
├── requirements.txt       # Python dependencies
├── .env.example          # Environment variables template
└── README.md             # This file
```

## 🛠️ Setup Instructions

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure Environment Variables

Copy `.env.example` to `.env` and fill in your credentials:

```bash
cp .env.example .env
```

Edit `.env` with your actual database and email credentials:

```env
# Oracle Database
ORACLE_USER=your_oracle_username
ORACLE_PASSWORD=your_oracle_password
ORACLE_HOST=your_oracle_host
ORACLE_SERVICE_NAME=your_service_name

# MySQL Database
MYSQL_HOST=your_mysql_host
MYSQL_USER=your_mysql_username
MYSQL_PASSWORD=your_mysql_password
MYSQL_DATABASE=your_mysql_database

# Email Configuration
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password
CLIENT_EMAIL=<EMAIL>
```

### 3. Test Connections

Before running automation, test all connections:

```bash
python test_connections.py
```

## 🎯 Usage

### Quick Start - Single Run

For testing or manual execution:

```bash
python run_once.py
```

### Custom Automation

Create your own automation script:

```python
from db_automation import DatabaseAutomation

# Your SQL queries
oracle_query = "SELECT id, name, salary FROM employees"
mysql_query = "SELECT id, email, status FROM user_details"

# Merge configuration
merge_config = {
    'type': 'merge',  # or 'concatenate'
    'on': 'id',
    'how': 'inner'
}

# Run automation
automation = DatabaseAutomation()
success = automation.run_automation(
    oracle_query=oracle_query,
    mysql_query=mysql_query,
    merge_config=merge_config,
    send_email=True,
    save_file=True
)
```

### Scheduled Automation

For continuous automated reports:

```bash
python scheduler.py
```

This will run:
- Daily reports at the configured time
- Weekly reports every Monday at 8:00 AM

## ⚙️ Configuration Options

### Merge Types

1. **Merge**: Join data on common columns
```python
merge_config = {
    'type': 'merge',
    'on': 'id',           # Column to merge on
    'how': 'inner'        # inner, outer, left, right
}
```

2. **Concatenate**: Stack data vertically
```python
merge_config = {
    'type': 'concatenate',
    'add_source_column': True  # Add source identifier
}
```

### Filters

Apply filters to the merged data:
```python
filters = {
    'status': 'active',
    'department_id': [10, 20, 30],
    'salary': {'min': 50000, 'max': 100000}
}
```

### Column Selection

Select specific columns for the output:
```python
columns = ['id', 'name', 'email', 'salary', 'status']
```

## 📧 Email Configuration

### Gmail Setup

1. Enable 2-Factor Authentication
2. Generate an App Password
3. Use the App Password in `EMAIL_PASSWORD`

### Other Email Providers

Update SMTP settings in `.env`:
```env
SMTP_SERVER=your.smtp.server
SMTP_PORT=587
```

## 📊 HTML Report Features

- **Responsive Design**: Works on all devices
- **Professional Styling**: Clean, modern appearance
- **Data Statistics**: Row/column counts
- **Timestamps**: Generation time tracking
- **Hover Effects**: Interactive table rows
- **Source Attribution**: Clear data source identification

## 🔧 Customization

### Custom Queries

Edit the queries in your automation script:

```python
oracle_query = """
SELECT 
    employee_id as id,
    first_name,
    last_name,
    department_id,
    salary,
    hire_date
FROM employees 
WHERE hire_date >= SYSDATE - 30
"""

mysql_query = """
SELECT 
    emp_id as id,
    email,
    phone,
    status,
    last_login
FROM employee_details 
WHERE status = 'active'
"""
```

### Custom Scheduling

Modify `scheduler.py` to add custom schedules:

```python
# Every hour
schedule.every().hour.do(hourly_job)

# Specific days
schedule.every().friday.at("17:00").do(weekly_summary)

# Every N minutes
schedule.every(30).minutes.do(frequent_check)
```

## 🐛 Troubleshooting

### Common Issues

1. **Oracle Connection Failed**
   - Check TNS configuration
   - Verify service name
   - Ensure Oracle client is installed

2. **MySQL Connection Failed**
   - Verify host and port
   - Check firewall settings
   - Confirm database exists

3. **Email Sending Failed**
   - Use App Password for Gmail
   - Check SMTP settings
   - Verify network connectivity

### Logs

Check log files for detailed error information:
- `db_automation.log` - Main automation logs
- `scheduler.log` - Scheduler logs

## 📈 Performance Tips

1. **Optimize Queries**: Use indexes and limit result sets
2. **Connection Pooling**: For high-frequency automation
3. **Batch Processing**: For large datasets
4. **Caching**: Cache static lookup data

## 🔒 Security Best Practices

1. **Environment Variables**: Never commit credentials to code
2. **App Passwords**: Use app-specific passwords for email
3. **Network Security**: Use VPN for database connections
4. **Regular Updates**: Keep dependencies updated

## 📝 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review log files
3. Test connections with `test_connections.py`
4. Create an issue with detailed error information
