"""
Setup script for Database Automation System
"""
import os
import sys
import subprocess

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Packages installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install packages: {e}")
        return False

def create_env_file():
    """Create .env file from template if it doesn't exist"""
    if os.path.exists('.env'):
        print("📄 .env file already exists")
        return True
    
    if not os.path.exists('.env.example'):
        print("❌ .env.example template not found")
        return False
    
    print("📄 Creating .env file from template...")
    try:
        with open('.env.example', 'r') as template:
            content = template.read()
        
        with open('.env', 'w') as env_file:
            env_file.write(content)
        
        print("✅ .env file created successfully!")
        print("⚠️  Please edit .env file with your actual credentials")
        return True
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
        return False

def create_reports_directory():
    """Create reports directory"""
    if not os.path.exists('reports'):
        print("📁 Creating reports directory...")
        os.makedirs('reports')
        print("✅ Reports directory created!")
    else:
        print("📁 Reports directory already exists")
    return True

def main():
    """Main setup function"""
    print("=" * 60)
    print("🚀 DATABASE AUTOMATION SYSTEM - SETUP")
    print("=" * 60)
    
    steps = [
        ("Installing Python packages", install_requirements),
        ("Creating environment file", create_env_file),
        ("Creating reports directory", create_reports_directory),
    ]
    
    success_count = 0
    
    for step_name, step_function in steps:
        print(f"\n{step_name}...")
        if step_function():
            success_count += 1
        else:
            print(f"❌ Failed: {step_name}")
    
    print("\n" + "=" * 60)
    print(f"Setup completed: {success_count}/{len(steps)} steps successful")
    
    if success_count == len(steps):
        print("🎉 Setup completed successfully!")
        print("\nNext steps:")
        print("1. Edit .env file with your database and email credentials")
        print("2. Run 'python test_connections.py' to test connections")
        print("3. Run 'python run_once.py' for a test automation")
        print("4. Run 'python scheduler.py' for continuous automation")
    else:
        print("⚠️  Setup completed with some issues. Please resolve them before proceeding.")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
