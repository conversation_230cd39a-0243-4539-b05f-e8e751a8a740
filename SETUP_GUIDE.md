# Database Automation Setup Guide

## 🚨 Network/Package Installation Issues

We encountered network connectivity issues when trying to install packages via pip. This is common in corporate environments due to firewall restrictions or proxy settings.

## 🔧 Solution: Simplified Version

I've created a **simplified version** that works with minimal dependencies and includes fallback options.

## 📁 Files Created

### Core Files (Work with existing packages):
- `simple_automation.py` - Main automation script (minimal dependencies)
- `simple_database_manager.py` - Database manager with fallbacks
- `simple_data_processor.py` - Data processing without pandas
- `html_generator.py` - HTML generation (uses existing Jinja2)
- `email_sender.py` - Email functionality
- `config.py` - Configuration (works without python-dotenv)

### Original Files (Require additional packages):
- `db_automation.py` - Full-featured version (requires pandas, etc.)
- `database_manager.py` - Original database manager
- `data_processor.py` - Original data processor

## 🚀 Quick Start (Simplified Version)

### 1. Test the Simplified Version

```bash
python simple_automation.py
```

This will:
- ✅ Use sample data if databases aren't connected
- ✅ Generate HTML report
- ✅ Save to `reports/` folder
- ✅ Work with existing packages

### 2. Configure Your Environment

Edit the configuration directly in `config.py` or set environment variables:

```python
# Option 1: Edit config.py directly
class DatabaseConfig:
    ORACLE_USER = "your_oracle_user"
    ORACLE_PASSWORD = "your_oracle_password"
    # ... etc
```

```bash
# Option 2: Set environment variables
set ORACLE_USER=your_oracle_user
set ORACLE_PASSWORD=your_oracle_password
set MYSQL_HOST=your_mysql_host
# ... etc
```

## 📦 Installing Missing Packages (When Network Allows)

### Method 1: Try Different Pip Options

```bash
# Try with trusted hosts
py -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org pandas

# Try with different timeout
py -m pip install --timeout 1000 pandas

# Try with proxy (if you have one)
py -m pip install --proxy http://proxy.company.com:8080 pandas
```

### Method 2: Download Packages Manually

1. Go to https://pypi.org/project/pandas/#files
2. Download the `.whl` file for Windows
3. Install locally:
```bash
py -m pip install path/to/downloaded/file.whl
```

### Method 3: Use Alternative Package Managers

If available in your environment:
```bash
# Conda
conda install pandas mysql-connector-python

# Chocolatey
choco install python-pandas
```

## 🔗 Required Packages for Full Functionality

| Package | Purpose | Status | Alternative |
|---------|---------|--------|-------------|
| `pandas` | Data manipulation | ❌ Missing | ✅ `simple_data_processor.py` |
| `python-dotenv` | Environment variables | ❌ Missing | ✅ Direct `os.environ` |
| `mysql-connector-python` | MySQL connectivity | ❌ Missing | ⚠️ Sample data fallback |
| `cx_Oracle` | Oracle connectivity | ❌ Missing | ⚠️ Sample data fallback |
| `schedule` | Task scheduling | ❌ Missing | ✅ Windows Task Scheduler |
| `jinja2` | HTML templating | ✅ Installed | ✅ Available |
| `requests` | HTTP requests | ✅ Installed | ✅ Available |

## 🎯 Current Capabilities

### ✅ What Works Now:
- HTML report generation with professional styling
- Data merging and processing (without pandas)
- Email sending (when configured)
- Sample data testing
- Error handling and logging
- Configuration management

### ⚠️ What Needs Database Drivers:
- Actual Oracle database connectivity (needs `cx_Oracle`)
- Actual MySQL database connectivity (needs `mysql-connector-python`)

### 🔄 Workarounds Available:
- Sample data for testing
- Manual package installation
- Environment variable configuration
- Simplified data processing

## 📋 Testing Steps

### 1. Test with Sample Data
```bash
python simple_automation.py
```

### 2. Test HTML Generation
Check the `reports/` folder for generated HTML files.

### 3. Test Email (Optional)
Configure email settings in `config.py` and test.

### 4. Test Database Connections (When drivers available)
```bash
python test_connections.py
```

## 🔧 Troubleshooting

### Network Issues
- Check corporate firewall settings
- Ask IT for proxy configuration
- Try downloading packages manually
- Use alternative package sources

### Database Issues
- Install database drivers manually
- Check connection strings
- Verify network access to databases
- Use sample data for initial testing

### Email Issues
- Use app passwords for Gmail
- Check SMTP settings
- Verify network access to email servers

## 📈 Next Steps

1. **Test the simplified version** to ensure basic functionality
2. **Configure your database credentials** when ready
3. **Install database drivers** when network allows:
   ```bash
   py -m pip install cx_Oracle mysql-connector-python
   ```
4. **Switch to full version** (`db_automation.py`) when all packages are available

## 🎉 Success Indicators

You'll know it's working when:
- ✅ HTML reports are generated in `reports/` folder
- ✅ Reports contain your data (or sample data)
- ✅ Email delivery works (if configured)
- ✅ No critical errors in logs

The system is designed to be **resilient** and **work incrementally** as you add more packages and configure more features.
