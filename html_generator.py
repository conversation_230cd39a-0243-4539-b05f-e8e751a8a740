"""
HTML table generation and styling
"""
import pandas as pd
import logging
from datetime import datetime
from jinja2 import Template
from config import ReportConfig

logger = logging.getLogger(__name__)

class HTMLGenerator:
    """Generates styled HTML tables from DataFrames"""
    
    def __init__(self):
        self.config = ReportConfig()
    
    def generate_html_table(self, 
                           df: pd.DataFrame, 
                           title: str = None,
                           include_styling: bool = True,
                           table_id: str = "data-table") -> str:
        """
        Generate HTML table from DataFrame
        
        Args:
            df: Input DataFrame
            title: Table title
            include_styling: Whether to include CSS styling
            table_id: HTML table ID
        
        Returns:
            HTML string
        """
        try:
            if title is None:
                title = self.config.REPORT_TITLE
            
            # Generate basic HTML table
            table_html = df.to_html(
                index=False,
                classes=f'table table-striped table-bordered {table_id}',
                border=0,
                escape=False
            )
            
            if include_styling:
                html_content = self._create_styled_html(table_html, title, df)
            else:
                html_content = table_html
            
            logger.info(f"Generated HTML table with {len(df)} rows and {len(df.columns)} columns")
            return html_content
            
        except Exception as e:
            logger.error(f"Error generating HTML table: {e}")
            raise
    
    def _create_styled_html(self, table_html: str, title: str, df: pd.DataFrame) -> str:
        """Create a complete styled HTML document"""
        
        template_str = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 2.5em;
        }
        .metadata {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 14px;
        }
        .table th {
            background-color: #007bff;
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: 600;
            border: 1px solid #dee2e6;
        }
        .table td {
            padding: 10px 8px;
            border: 1px solid #dee2e6;
        }
        .table-striped tbody tr:nth-of-type(odd) {
            background-color: #f8f9fa;
        }
        .table-bordered {
            border: 1px solid #dee2e6;
        }
        .table tbody tr:hover {
            background-color: #e3f2fd;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            color: #666;
            font-size: 12px;
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
        }
        .stat-item {
            text-align: center;
            padding: 10px;
            background-color: #e3f2fd;
            border-radius: 5px;
            min-width: 100px;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ title }}</h1>
        </div>
        
        <div class="metadata">
            <strong>Report Generated:</strong> {{ timestamp }}<br>
            <strong>Data Sources:</strong> Oracle Database + MySQL Database
        </div>
        
        <div class="stats">
            <div class="stat-item">
                <div class="stat-number">{{ row_count }}</div>
                <div class="stat-label">Total Rows</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">{{ col_count }}</div>
                <div class="stat-label">Columns</div>
            </div>
        </div>
        
        {{ table_html }}
        
        <div class="footer">
            <p>This report was automatically generated by the Database Automation System</p>
            <p>Generated on {{ timestamp }}</p>
        </div>
    </div>
</body>
</html>
        """
        
        template = Template(template_str)
        
        return template.render(
            title=title,
            table_html=table_html,
            timestamp=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            row_count=len(df),
            col_count=len(df.columns)
        )
    
    def save_html_file(self, html_content: str, filename: str = None) -> str:
        """
        Save HTML content to file
        
        Args:
            html_content: HTML string to save
            filename: Output filename (optional)
        
        Returns:
            Path to saved file
        """
        try:
            self.config.ensure_output_dir()
            
            if filename is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"database_report_{timestamp}.html"
            
            filepath = f"{self.config.OUTPUT_DIR}/{filename}"
            
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"HTML report saved to: {filepath}")
            return filepath
            
        except Exception as e:
            logger.error(f"Error saving HTML file: {e}")
            raise
