# Core packages (install these if possible)
# pandas  # For data manipulation - can be replaced with basic Python
# python-dotenv  # For environment variables - can be replaced with os.environ
# schedule  # For scheduling - can be replaced with Windows Task Scheduler
# mysql-connector-python  # For MySQL - required for MySQL connectivity
# cx_Oracle  # For Oracle - required for Oracle connectivity

# Already installed packages that we'll use:
# jinja2>=3.1.0  # Already installed (3.1.4)
# requests>=2.32.0  # Already installed (2.32.3)
# numpy>=2.1.0  # Already installed (2.1.3)
