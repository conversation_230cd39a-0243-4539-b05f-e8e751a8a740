"""
<PERSON><PERSON><PERSON> to run the automation once (for testing or manual execution)
"""
import logging
import sys
from db_automation import DatabaseAutomation

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def main():
    """Run automation once with sample queries"""
    
    print("=" * 50)
    print("DATABASE AUTOMATION - SINGLE RUN")
    print("=" * 50)
    
    # Sample queries - REPLACE THESE WITH YOUR ACTUAL QUERIES
    oracle_query = """
    SELECT 
        1 as id,
        '<PERSON>' as first_name,
        '<PERSON><PERSON>' as last_name,
        10 as department_id,
        50000 as salary
    FROM DUAL
    UNION ALL
    SELECT 
        2 as id,
        '<PERSON>' as first_name,
        '<PERSON>' as last_name,
        20 as department_id,
        60000 as salary
    FROM DUAL
    """
    
    mysql_query = """
    SELECT 
        1 as id,
        '<EMAIL>' as email,
        '+1234567890' as phone,
        'active' as status
    UNION ALL
    SELECT 
        2 as id,
        '<EMAIL>' as email,
        '+1234567891' as phone,
        'active' as status
    """
    
    # Configuration
    merge_config = {
        'type': 'merge',
        'on': 'id',
        'how': 'inner'
    }
    
    filters = {
        'status': 'active'
    }
    
    columns = ['id', 'first_name', 'last_name', 'email', 'salary', 'status']
    
    # Run automation
    automation = DatabaseAutomation()
    
    print("Testing connections...")
    connection_results = automation.test_connections()
    
    if not all(connection_results.values()):
        print("❌ Connection test failed. Please run test_connections.py for details.")
        return False
    
    print("✅ All connections successful!")
    print("\nRunning automation...")
    
    success = automation.run_automation(
        oracle_query=oracle_query,
        mysql_query=mysql_query,
        merge_config=merge_config,
        filters=filters,
        columns=columns,
        send_email=True,
        save_file=True
    )
    
    if success:
        print("🎉 Automation completed successfully!")
        print("Check your email and the 'reports' folder for the generated report.")
    else:
        print("❌ Automation failed! Check the logs for details.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
