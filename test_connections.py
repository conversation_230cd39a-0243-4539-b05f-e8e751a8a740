"""
Test script to verify all connections are working
"""
import logging
import sys
from db_automation import DatabaseAutomation

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def main():
    """Test all connections"""
    print("=" * 50)
    print("DATABASE AUTOMATION - CONNECTION TEST")
    print("=" * 50)
    
    automation = DatabaseAutomation()
    
    # Test all connections
    results = automation.test_connections()
    
    print("\nConnection Test Results:")
    print("-" * 30)
    
    for service, status in results.items():
        status_text = "✅ SUCCESS" if status else "❌ FAILED"
        print(f"{service.upper():<10}: {status_text}")
    
    print("-" * 30)
    
    if all(results.values()):
        print("🎉 All connections successful! You're ready to run automation.")
        return True
    else:
        print("⚠️  Some connections failed. Please check your configuration.")
        print("\nTroubleshooting tips:")
        
        if not results.get('oracle', True):
            print("- Oracle: Check ORACLE_USER, OR<PERSON>LE_PASSWORD, OR<PERSON>LE_HOST, ORACLE_SERVICE_NAME in .env")
        
        if not results.get('mysql', True):
            print("- MySQL: Check MYSQL_HOST, MYSQL_USER, MYSQL_PASSWORD, MYSQL_DATABASE in .env")
        
        if not results.get('email', True):
            print("- Email: Check EMAIL_USER, EMAIL_PASSWORD, SMTP_SERVER settings in .env")
            print("- For Gmail: Use App Password instead of regular password")
        
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
