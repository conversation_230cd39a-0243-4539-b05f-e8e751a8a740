"""
Simple data processing without pandas dependency
"""
import logging
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class SimpleDataProcessor:
    """Handles data merging and processing operations without pandas"""
    
    def __init__(self):
        pass
    
    def dict_list_to_table(self, data: List[Dict]) -> Dict:
        """Convert list of dictionaries to table format"""
        if not data:
            return {'columns': [], 'rows': []}
        
        # Get all unique columns
        columns = set()
        for row in data:
            columns.update(row.keys())
        columns = sorted(list(columns))
        
        # Convert to rows
        rows = []
        for row_dict in data:
            row = [row_dict.get(col, '') for col in columns]
            rows.append(row)
        
        return {'columns': columns, 'rows': rows}
    
    def merge_data(self, 
                   oracle_data: List[Dict], 
                   mysql_data: List[Dict],
                   merge_on: str = 'id',
                   merge_type: str = 'inner') -> List[Dict]:
        """
        Merge Oracle and MySQL data
        
        Args:
            oracle_data: List of dictionaries from Oracle
            mysql_data: List of dictionaries from MySQL
            merge_on: Column name to merge on
            merge_type: Type of merge ('inner', 'outer', 'left', 'right')
        
        Returns:
            List of merged dictionaries
        """
        try:
            # Create lookup dictionaries
            oracle_lookup = {row.get(merge_on): row for row in oracle_data if merge_on in row}
            mysql_lookup = {row.get(merge_on): row for row in mysql_data if merge_on in row}
            
            merged_data = []
            
            if merge_type == 'inner':
                # Only keys that exist in both
                common_keys = set(oracle_lookup.keys()) & set(mysql_lookup.keys())
                for key in common_keys:
                    merged_row = {}
                    merged_row.update(oracle_lookup[key])
                    # Add MySQL data with suffix for conflicts
                    for k, v in mysql_lookup[key].items():
                        if k in merged_row and k != merge_on:
                            merged_row[f"{k}_mysql"] = v
                            if f"{k}_oracle" not in merged_row:
                                merged_row[f"{k}_oracle"] = merged_row[k]
                                del merged_row[k]
                        else:
                            merged_row[k] = v
                    merged_data.append(merged_row)
            
            elif merge_type == 'left':
                # All Oracle keys
                for key, oracle_row in oracle_lookup.items():
                    merged_row = oracle_row.copy()
                    if key in mysql_lookup:
                        for k, v in mysql_lookup[key].items():
                            if k in merged_row and k != merge_on:
                                merged_row[f"{k}_mysql"] = v
                                merged_row[f"{k}_oracle"] = merged_row[k]
                                del merged_row[k]
                            else:
                                merged_row[k] = v
                    merged_data.append(merged_row)
            
            elif merge_type == 'right':
                # All MySQL keys
                for key, mysql_row in mysql_lookup.items():
                    merged_row = mysql_row.copy()
                    if key in oracle_lookup:
                        for k, v in oracle_lookup[key].items():
                            if k in merged_row and k != merge_on:
                                merged_row[f"{k}_oracle"] = v
                                merged_row[f"{k}_mysql"] = merged_row[k]
                                del merged_row[k]
                            else:
                                merged_row[k] = v
                    merged_data.append(merged_row)
            
            elif merge_type == 'outer':
                # All keys from both
                all_keys = set(oracle_lookup.keys()) | set(mysql_lookup.keys())
                for key in all_keys:
                    merged_row = {}
                    if key in oracle_lookup:
                        merged_row.update(oracle_lookup[key])
                    if key in mysql_lookup:
                        for k, v in mysql_lookup[key].items():
                            if k in merged_row and k != merge_on:
                                merged_row[f"{k}_mysql"] = v
                                if f"{k}_oracle" not in merged_row:
                                    merged_row[f"{k}_oracle"] = merged_row[k]
                                    del merged_row[k]
                            else:
                                merged_row[k] = v
                    merged_data.append(merged_row)
            
            logger.info(f"Merged data: {len(merged_data)} rows from {len(oracle_data)} Oracle + {len(mysql_data)} MySQL rows")
            return merged_data
            
        except Exception as e:
            logger.error(f"Error merging data: {e}")
            raise
    
    def concatenate_data(self, 
                        oracle_data: List[Dict], 
                        mysql_data: List[Dict],
                        add_source_column: bool = True) -> List[Dict]:
        """
        Concatenate Oracle and MySQL data
        
        Args:
            oracle_data: List of dictionaries from Oracle
            mysql_data: List of dictionaries from MySQL
            add_source_column: Whether to add a source identifier column
        
        Returns:
            List of concatenated dictionaries
        """
        try:
            concatenated_data = []
            
            # Add Oracle data
            for row in oracle_data:
                new_row = row.copy()
                if add_source_column:
                    new_row['data_source'] = 'Oracle'
                concatenated_data.append(new_row)
            
            # Add MySQL data
            for row in mysql_data:
                new_row = row.copy()
                if add_source_column:
                    new_row['data_source'] = 'MySQL'
                concatenated_data.append(new_row)
            
            logger.info(f"Concatenated data: {len(concatenated_data)} total rows")
            return concatenated_data
            
        except Exception as e:
            logger.error(f"Error concatenating data: {e}")
            raise
    
    def apply_filters(self, data: List[Dict], filters: Dict) -> List[Dict]:
        """
        Apply filters to the data
        
        Args:
            data: List of dictionaries
            filters: Dictionary of column:value filters
        
        Returns:
            Filtered list of dictionaries
        """
        try:
            filtered_data = []
            
            for row in data:
                include_row = True
                
                for column, value in filters.items():
                    if column in row:
                        if isinstance(value, list):
                            if row[column] not in value:
                                include_row = False
                                break
                        else:
                            if row[column] != value:
                                include_row = False
                                break
                    else:
                        # Column not found, exclude row
                        include_row = False
                        break
                
                if include_row:
                    filtered_data.append(row)
            
            logger.info(f"Applied filters, remaining rows: {len(filtered_data)}")
            return filtered_data
            
        except Exception as e:
            logger.error(f"Error applying filters: {e}")
            raise
    
    def select_columns(self, data: List[Dict], columns: List[str]) -> List[Dict]:
        """
        Select specific columns from data
        
        Args:
            data: List of dictionaries
            columns: List of column names to select
        
        Returns:
            List of dictionaries with selected columns
        """
        try:
            selected_data = []
            
            for row in data:
                new_row = {}
                for col in columns:
                    if col in row:
                        new_row[col] = row[col]
                    else:
                        new_row[col] = ''  # Empty value for missing columns
                selected_data.append(new_row)
            
            logger.info(f"Selected {len(columns)} columns")
            return selected_data
            
        except Exception as e:
            logger.error(f"Error selecting columns: {e}")
            raise
